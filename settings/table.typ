// ================================================================
//                    表格处理文件 (Table Processing)
// ================================================================

// --- 全局表格计数器 ---
#let table-counter = counter("global-table")

// --- 简化三线表样式设置 ---
#let three-line-table(
  data,
  caption: none,
  adaptive-width: false,  // 是否使用自适应列宽
  full-width: true        // 是否占满页面宽度
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
    ]
  ]

  v(0.5em)

  // 再显示表格 - 支持自适应列宽和全宽度控制
  if full-width {
    // 占满页面宽度的表格
    align(center)[
      #table(
        // 根据参数选择列宽策略
        columns: if adaptive-width {
          // 自适应列宽但保持全宽度：使用fr单位但按内容比例分配
          let col-count = data.at(0).len()
          if col-count <= 2 {
            (1fr,) * col-count
          } else if col-count == 3 {
            (1.5fr, 1fr, 1fr)
          } else if col-count == 4 {
            (2fr, 1fr, 1fr, 1fr)
          } else {
            (2fr,) + (1fr,) * (col-count - 1)
          }
        } else {
          // 平均分配列宽
          (1fr,) * data.at(0).len()
        },
        align: center,  // 保持居中对齐
        inset: 8pt,
        table.header(
          table.hline(stroke: 0.8pt),  // 表头顶部粗线
          ..data.at(0).map(cell => strong(cell)),  // 表头内容
          table.hline(stroke: 0.5pt),  // 表头下细线
        ),
        stroke: (x, y) => {
          if y == data.len() - 1 { (bottom: 0.8pt) } // 表格底部粗线
          else { none }                               // 其他位置无线
        },

        // 数据行 - 简化处理，让Typst自动处理换行
        ..data.slice(1).flatten()
      )
    ]
  } else {
    // 紧凑宽度的表格（根据内容调整）
    align(center)[
      #table(
        // 使用auto让表格根据内容调整宽度
        columns: (auto,) * data.at(0).len(),
        align: center,
        inset: 8pt,
        table.header(
          table.hline(stroke: 0.8pt),
          ..data.at(0).map(cell => strong(cell)),
          table.hline(stroke: 0.5pt),
        ),
        stroke: (x, y) => {
          if y == data.len() - 1 { (bottom: 0.8pt) }
          else { none }
        },

        ..data.slice(1).flatten()
      )
    ]
  }
}

// --- CSV读取函数 ---
#let load-csv-simple(
  file-path,
  caption: none,
  adaptive-width: false,
  full-width: true
) = {
  let data = csv(file-path)
  three-line-table(data, caption: caption, adaptive-width: adaptive-width, full-width: full-width)
}

// --- CSV自适应宽度表格 ---
#let load-csv-adaptive(
  file-path,
  caption: none,
  width-strategy: "auto"  // "auto", "balanced", 或自定义数组
) = {
  let data = csv(file-path)

  if width-strategy == "auto" {
    // 使用完全自适应宽度
    three-line-table(data, caption: caption, adaptive-width: true)
  } else if width-strategy == "balanced" {
    // 使用平衡策略
    let col-count = data.at(0).len()
    let balanced-widths = if col-count == 1 {
      (1fr,)
    } else if col-count == 2 {
      (1.5fr, 1fr)
    } else if col-count == 3 {
      (1.5fr, 1fr, 1fr)
    } else if col-count == 4 {
      (2fr, 1fr, 1fr, 1fr)
    } else {
      // 超过4列时，第一列2fr，其他列1fr
      (2fr,) + (1fr,) * (col-count - 1)
    }

    // 步进表格计数器
    table-counter.step()

    // 获取当前章节号和表格序号
    let chapter-num = context counter(heading).display("1")
    let table-num = context table-counter.display("1")

    // 设置表格样式
    set text(size: 10.5pt)

    // 先显示标题
    context [
      #align(center)[
        #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
      ]
    ]

    v(0.5em)

    // 显示表格
    align(center)[
      #table(
        columns: balanced-widths,
        align: center,
        inset: 8pt,
        table.header(
          table.hline(stroke: 0.8pt),
          ..data.at(0).map(cell => strong(cell)),
          table.hline(stroke: 0.5pt),
        ),
        stroke: (x, y) => {
          if y == data.len() - 1 { (bottom: 0.8pt) }
          else { none }
        },

        ..data.slice(1).flatten()
      )
    ]
  } else if type(width-strategy) == array {
    // 使用自定义列宽数组
    // 步进表格计数器
    table-counter.step()

    // 获取当前章节号和表格序号
    let chapter-num = context counter(heading).display("1")
    let table-num = context table-counter.display("1")

    // 设置表格样式
    set text(size: 10.5pt)

    // 先显示标题
    context [
      #align(center)[
        #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
      ]
    ]

    v(0.5em)

    // 显示表格
    align(center)[
      #table(
        columns: width-strategy,
        align: center,
        inset: 8pt,
        table.header(
          table.hline(stroke: 0.8pt),
          ..data.at(0).map(cell => strong(cell)),
          table.hline(stroke: 0.5pt),
        ),
        stroke: (x, y) => {
          if y == data.len() - 1 { (bottom: 0.8pt) }
          else { none }
        },

        ..data.slice(1).flatten()
      )
    ]
  } else {
    // 默认使用平均分配
    three-line-table(data, caption: caption, adaptive-width: false)
  }
}

// --- 自适应宽度表格函数 ---
#let adaptive-width-table(
  data,
  caption: none,
  column-widths: none  // 可选：手动指定列宽比例，如 (2fr, 1fr, 1fr)
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
    ]
  ]

  v(0.5em)

  // 显示表格
  align(center)[
    #table(
      // 智能列宽策略
      columns: if column-widths != none {
        // 使用手动指定的列宽
        column-widths
      } else {
        // 使用自适应列宽
        auto * data.at(0).len()
      },
      align: center,
      inset: 8pt,
      table.header(
        table.hline(stroke: 0.8pt),
        ..data.at(0).map(cell => strong(cell)),
        table.hline(stroke: 0.5pt),
      ),
      stroke: (x, y) => {
        if y == data.len() - 1 { (bottom: 0.8pt) }
        else { none }
      },

      ..data.slice(1).flatten()
    )
  ]
}

// --- 混合宽度策略表格 ---
#let mixed-width-table(
  data,
  caption: none,
  width-strategy: "balanced"  // "auto", "equal", "balanced", 或自定义数组
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
    ]
  ]

  v(0.5em)

  // 显示表格
  align(center)[
    #table(
      // 根据策略设置列宽
      columns: if type(width-strategy) == array {
        // 自定义列宽数组
        width-strategy
      } else if width-strategy == "auto" {
        // 完全自适应
        auto * data.at(0).len()
      } else if width-strategy == "equal" {
        // 平均分配
        (1fr,) * data.at(0).len()
      } else if width-strategy == "balanced" {
        // 平衡策略：第一列稍宽，其他列自适应
        let col-count = data.at(0).len()
        if col-count == 1 {
          (1fr,)
        } else if col-count == 2 {
          (1.5fr, 1fr)
        } else if col-count == 3 {
          (1.5fr, 1fr, 1fr)
        } else if col-count == 4 {
          (2fr, 1fr, 1fr, 1fr)
        } else {
          // 超过4列时，第一列2fr，其他列1fr
          (2fr,) + (1fr,) * (col-count - 1)
        }
      } else {
        // 默认平均分配
        (1fr,) * data.at(0).len()
      },
      align: center,
      inset: 8pt,
      table.header(
        table.hline(stroke: 0.8pt),
        ..data.at(0).map(cell => strong(cell)),
        table.hline(stroke: 0.5pt),
      ),
      stroke: (x, y) => {
        if y == data.len() - 1 { (bottom: 0.8pt) }
        else { none }
      },

      ..data.slice(1).flatten()
    )
  ]
}

// --- 示例：result.csv数据表格（传统平均宽度） ---
#let result-table(caption: "实验结果数据") = {
  load-csv-simple("../assets/data/result.csv", caption: caption)
}

// --- result.csv自适应宽度表格 ---
#let result-table-adaptive(caption: "实验结果数据") = {
  load-csv-adaptive("../assets/data/result.csv", caption: caption)
}

// --- result.csv平衡宽度表格 ---
#let result-table-balanced(caption: "实验结果数据") = {
  load-csv-adaptive("../assets/data/result.csv", caption: caption, width-strategy: "balanced")
}

// --- 手动创建表格 ---
#let manual-table(
  headers,
  rows,
  caption: none,
  adaptive-width: false,
  full-width: true
) = {
  let data = (headers,) + rows
  three-line-table(data, caption: caption, adaptive-width: adaptive-width, full-width: full-width)
}

// --- 手动创建自适应表格 ---
#let manual-adaptive-table(
  headers,
  rows,
  caption: none,
  width-strategy: "balanced"  // "auto", "balanced", 或自定义数组
) = {
  let data = (headers,) + rows

  if width-strategy == "auto" {
    // 使用完全自适应宽度
    three-line-table(data, caption: caption, adaptive-width: true)
  } else if width-strategy == "balanced" {
    // 使用平衡策略
    let col-count = data.at(0).len()
    let balanced-widths = if col-count == 1 {
      (1fr,)
    } else if col-count == 2 {
      (1.5fr, 1fr)
    } else if col-count == 3 {
      (1.5fr, 1fr, 1fr)
    } else if col-count == 4 {
      (2fr, 1fr, 1fr, 1fr)
    } else {
      // 超过4列时，第一列2fr，其他列1fr
      (2fr,) + (1fr,) * (col-count - 1)
    }

    // 步进表格计数器
    table-counter.step()

    // 获取当前章节号和表格序号
    let chapter-num = context counter(heading).display("1")
    let table-num = context table-counter.display("1")

    // 设置表格样式
    set text(size: 10.5pt)

    // 先显示标题
    context [
      #align(center)[
        #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
      ]
    ]

    v(0.5em)

    // 显示表格
    align(center)[
      #table(
        columns: balanced-widths,
        align: center,
        inset: 8pt,
        table.header(
          table.hline(stroke: 0.8pt),
          ..data.at(0).map(cell => strong(cell)),
          table.hline(stroke: 0.5pt),
        ),
        stroke: (x, y) => {
          if y == data.len() - 1 { (bottom: 0.8pt) }
          else { none }
        },

        ..data.slice(1).flatten()
      )
    ]
  } else if type(width-strategy) == array {
    // 使用自定义列宽数组
    // 步进表格计数器
    table-counter.step()

    // 获取当前章节号和表格序号
    let chapter-num = context counter(heading).display("1")
    let table-num = context table-counter.display("1")

    // 设置表格样式
    set text(size: 10.5pt)

    // 先显示标题
    context [
      #align(center)[
        #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
      ]
    ]

    v(0.5em)

    // 显示表格
    align(center)[
      #table(
        columns: width-strategy,
        align: center,
        inset: 8pt,
        table.header(
          table.hline(stroke: 0.8pt),
          ..data.at(0).map(cell => strong(cell)),
          table.hline(stroke: 0.5pt),
        ),
        stroke: (x, y) => {
          if y == data.len() - 1 { (bottom: 0.8pt) }
          else { none }
        },

        ..data.slice(1).flatten()
      )
    ]
  } else {
    // 默认使用平均分配
    three-line-table(data, caption: caption, adaptive-width: false)
  }
}





