// ================================================================
//                    表格处理文件 (Table Processing)
// ================================================================

// --- 全局表格计数器 ---
#let table-counter = counter("global-table")

// --- 简化三线表样式设置 ---
#let three-line-table(
  data,
  caption: none,
  adaptive-width: false  // 新增参数：是否使用自适应列宽
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
    ]
  ]

  v(0.5em)

  // 再显示表格 - 支持自适应列宽
  align(center)[
    #table(
      // 根据参数选择列宽策略
      columns: if adaptive-width {
        // 自适应列宽：让Typst根据内容自动调整
        auto * data.at(0).len()
      } else {
        // 平均分配列宽
        (1fr,) * data.at(0).len()
      },
      align: center,  // 保持居中对齐
      inset: 8pt,
      table.header(
        table.hline(stroke: 0.8pt),  // 表头顶部粗线
        ..data.at(0).map(cell => strong(cell)),  // 表头内容
        table.hline(stroke: 0.5pt),  // 表头下细线
      ),
      stroke: (x, y) => {
        if y == data.len() - 1 { (bottom: 0.8pt) } // 表格底部粗线
        else { none }                               // 其他位置无线
      },

      // 数据行 - 简化处理，让Typst自动处理换行
      ..data.slice(1).flatten()
    )
  ]
}

// --- CSV读取函数 ---
#let load-csv-simple(
  file-path,
  caption: none,
  adaptive-width: false
) = {
  let data = csv(file-path)
  three-line-table(data, caption: caption, adaptive-width: adaptive-width)
}

// --- 自适应宽度表格函数 ---
#let adaptive-width-table(
  data,
  caption: none,
  column-widths: none  // 可选：手动指定列宽比例，如 (2fr, 1fr, 1fr)
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
    ]
  ]

  v(0.5em)

  // 显示表格
  align(center)[
    #table(
      // 智能列宽策略
      columns: if column-widths != none {
        // 使用手动指定的列宽
        column-widths
      } else {
        // 使用自适应列宽
        auto * data.at(0).len()
      },
      align: center,
      inset: 8pt,
      table.header(
        table.hline(stroke: 0.8pt),
        ..data.at(0).map(cell => strong(cell)),
        table.hline(stroke: 0.5pt),
      ),
      stroke: (x, y) => {
        if y == data.len() - 1 { (bottom: 0.8pt) }
        else { none }
      },

      ..data.slice(1).flatten()
    )
  ]
}

// --- 混合宽度策略表格 ---
#let mixed-width-table(
  data,
  caption: none,
  width-strategy: "balanced"  // "auto", "equal", "balanced", 或自定义数组
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 先显示标题
  context [
    #align(center)[
      #text(weight: "bold")[表 #chapter-num - #table-num: #caption]
    ]
  ]

  v(0.5em)

  // 显示表格
  align(center)[
    #table(
      // 根据策略设置列宽
      columns: if type(width-strategy) == array {
        // 自定义列宽数组
        width-strategy
      } else if width-strategy == "auto" {
        // 完全自适应
        auto * data.at(0).len()
      } else if width-strategy == "equal" {
        // 平均分配
        (1fr,) * data.at(0).len()
      } else if width-strategy == "balanced" {
        // 平衡策略：第一列稍宽，其他列自适应
        let col-count = data.at(0).len()
        if col-count == 1 {
          (1fr,)
        } else if col-count == 2 {
          (1.5fr, 1fr)
        } else if col-count == 3 {
          (1.5fr, 1fr, 1fr)
        } else if col-count == 4 {
          (2fr, 1fr, 1fr, 1fr)
        } else {
          // 超过4列时，第一列2fr，其他列1fr
          (2fr,) + (1fr,) * (col-count - 1)
        }
      } else {
        // 默认平均分配
        (1fr,) * data.at(0).len()
      },
      align: center,
      inset: 8pt,
      table.header(
        table.hline(stroke: 0.8pt),
        ..data.at(0).map(cell => strong(cell)),
        table.hline(stroke: 0.5pt),
      ),
      stroke: (x, y) => {
        if y == data.len() - 1 { (bottom: 0.8pt) }
        else { none }
      },

      ..data.slice(1).flatten()
    )
  ]
}

// --- 示例：result.csv数据表格 ---
#let result-table(caption: "实验结果数据") = {
  load-csv-simple("../assets/data/result.csv", caption: caption)
}

// --- 自适应result表格 ---
#let result-table-adaptive(caption: "实验结果数据") = {
  load-csv-simple("../assets/data/result.csv", caption: caption, adaptive-width: true)
}

// --- 手动创建表格 ---
#let manual-table(
  headers,
  rows,
  caption: none
) = {
  let data = (headers,) + rows
  three-line-table(data, caption: caption)
}
// ================================================================
//                表格处理文件 (Table Processing)
// ================================================================

// --- 全局表格计数器 ---
#let table-counter = counter("global-table")

// --- 简化三线表样式设置 ---
#let three-line-table(
  data,
  caption: none
) = {
  // 步进表格计数器
  table-counter.step()
  
  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")
  
  // 设置表格样式
  set text(size: 10.5pt)
  
  // 先显示标题
  context [
    #align(center)[
      // === 修改部分 开始 ===
      // 修复了变量名和连字符连接时产生的解析错误
      #text(weight: "bold")[表 #{chapter-num}-#{table-num}: #caption]
      // === 修改部分 结束 ===
    ]
  ]
  
  v(0.5em)
  
  // 再显示表格
  align(center)[
    #table(
      // 使用 `1fr` 让所有列平分页面宽度，从而实现自动换行
      columns: (1fr,) * data.at(0).len(),

      // 对于多行文本，建议使用 `horizon` (水平对齐到起点)
      align: horizon,

      inset: 8pt,
      stroke: none, // 将stroke移到下方统一处理
      
      // 表头
      table.header(
        // 为保持表头居中，可在此处单独设置
        ..data.at(0).map(cell => align(center, strong(cell))),
      ),

      // 表格的水平线 (三线表)
      table.hline(y: 0, stroke: 1.5pt), // 表格顶线，加粗
      table.hline(y: 1, stroke: 0.8pt), // 表头下线
      table.hline(y: data.len(), stroke: 1.5pt), // 表格底线，加粗
      
      // 数据行
      ..data.slice(1).flatten()
    )
  ]
}

// --- CSV读取函数（支持加粗标记） ---
#let load-csv-simple(
  file-path,
  caption: none
) = {
  let data = csv(file-path)

  // 处理数据，识别加粗标记
  let processed-data = data.map(row =>
    row.map(cell => {
      // 检查是否有加粗标记 **text** 或 *text*
      if cell.starts-with("**") and cell.ends-with("**") {
        // 双星号标记，去掉标记并加粗
        strong(cell.slice(2, -2))
      } else if cell.starts-with("*") and cell.ends-with("*") {
        // 单星号标记，去掉标记并加粗
        strong(cell.slice(1, -1))
      } else {
        cell
      }
    })
  )

  three-line-table(processed-data, caption: caption)
}

// --- 示例：result.csv数据表格 ---
#let result-table(caption: "实验结果数据") = {
  load-csv-simple("../assets/data/result.csv", caption: caption)
}

// --- 带条件加粗的CSV表格函数 ---
#let load-csv-with-bold(
  file-path,
  caption: none,
  bold-conditions: ()  // 格式: ((row, col), (row, col), ...)
) = {
  let data = csv(file-path)

  // 处理数据，对指定位置的单元格加粗
  let processed-data = data.enumerate().map(((row-idx, row)) => {
    row.enumerate().map(((col-idx, cell)) => {
      // 检查当前位置是否需要加粗
      if bold-conditions.any(((r, c)) => r == row-idx and c == col-idx) {
        strong(cell)
      } else {
        cell
      }
    })
  })

  three-line-table(processed-data, caption: caption)
}

// --- 带最佳值高亮的result表格 ---
#let result-table-highlighted(caption: "实验结果数据") = {
  let data = csv("../assets/data/result.csv")

  // 找到每列的最佳值（除了第一列方法名和最后一列时间）
  let processed-data = data.enumerate().map(((row-idx, row)) => {
    if row-idx == 0 {
      // 表头行，全部加粗
      row.map(cell => strong(cell))
    } else {
      // 数据行
      row.enumerate().map(((col-idx, cell)) => {
        // 对于准确率、召回率、F1分数列（索引1,2,3），找最大值并加粗
        if col-idx >= 1 and col-idx <= 3 {
          let col-values = data.slice(1).map(r => float(r.at(col-idx)))
          let max-value = calc.max(..col-values)
          if float(cell) == max-value {
            strong(cell)
          } else {
            cell
          }
        } else {
          cell
        }
      })
    }
  })

  three-line-table(processed-data, caption: caption)
}

// --- 手动创建表格 ---
#let manual-table(
  headers,
  rows,
  caption: none
) = {
  let data = (headers,) + rows
  three-line-table(data, caption: caption)
}

// --- 自动等宽三线表 (支持简单table语法) ---
#let auto-width-table(
  columns: auto,
  caption: none,
  ..content
) = {
  // 步进表格计数器
  table-counter.step()

  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")

  // 设置表格样式
  set text(size: 10.5pt)

  // 如果提供了标题，先显示标题
  if caption != none {
    context [
      #align(center)[
        #text(weight: "bold")[表 #{chapter-num}-#{table-num}: #caption]
      ]
    ]
    v(0.5em)
  }

  // 显示表格
  align(center)[
    #table(
      // 自动计算列数并设置为等宽
      columns: if type(columns) == int {
        (1fr,) * columns
      } else {
        (1fr,) * columns
      },

      // 水平对齐
      align: horizon,

      inset: 8pt,
      stroke: none,

      // 获取所有内容
      ..content.pos(),

      // 添加三线表样式
      // 顶线
      table.hline(y: 0, stroke: 1.5pt),
      // 表头下线 (假设第一行是表头)
      table.hline(y: 1, stroke: 0.8pt),
      // 底线 (需要计算总行数)
      table.hline(y: calc.ceil(content.pos().len() / columns), stroke: 1.5pt),
    )
  ]
}

// --- 简化的三线表函数 (直接替换table使用) ---
#let styled-table(
  columns: auto,
  caption: none,
  ..content
) = {
  // 如果没有提供标题，直接显示表格
  if caption == none {
    align(center)[
      #table(
        // 自动设置等宽列
        columns: if type(columns) == int {
          (1fr,) * columns
        } else {
          (1fr,) * columns
        },

        // 水平对齐
        align: horizon,

        inset: 8pt,
        stroke: none,

        // 获取所有内容
        ..content.pos(),

        // 添加三线表样式
        // 顶线
        table.hline(y: 0, stroke: 1.5pt),
        // 表头下线 (假设第一行是表头)
        table.hline(y: 1, stroke: 0.8pt),
        // 底线 (需要计算总行数)
        table.hline(y: calc.ceil(content.pos().len() / columns), stroke: 1.5pt),
      )
    ]
  } else {
    // 如果有标题，使用带编号的版本
    auto-width-table(columns: columns, caption: caption, ..content)
  }
}

// --- 使用分号分隔的简单表格 ---
#let simple-table(
  columns: auto,
  caption: none,
  data: ""
) = {
  // 按分号分割数据
  let cells = data.split(";").map(cell => cell.trim())

  // 如果没有提供标题，直接显示表格
  if caption == none {
    align(center)[
      #table(
        // 自动设置等宽列
        columns: if type(columns) == int {
          (1fr,) * columns
        } else {
          (1fr,) * columns
        },

        // 水平对齐
        align: horizon,

        inset: 8pt,
        stroke: none,

        // 将分割后的数据作为表格内容
        ..cells,

        // 添加三线表样式
        // 顶线
        table.hline(y: 0, stroke: 1.5pt),
        // 表头下线 (假设第一行是表头)
        table.hline(y: 1, stroke: 0.8pt),
        // 底线 (需要计算总行数)
        table.hline(y: calc.ceil(cells.len() / columns), stroke: 1.5pt),
      )
    ]
  } else {
    // 如果有标题，使用带编号的版本
    // 步进表格计数器
    table-counter.step()

    // 获取当前章节号和表格序号
    let chapter-num = context counter(heading).display("1")
    let table-num = context table-counter.display("1")

    // 设置表格样式
    set text(size: 10.5pt)

    // 显示标题
    context [
      #align(center)[
        #text(weight: "bold")[表 #{chapter-num}-#{table-num}: #caption]
      ]
    ]
    v(0.5em)

    // 显示表格
    align(center)[
      #table(
        // 自动设置等宽列
        columns: if type(columns) == int {
          (1fr,) * columns
        } else {
          (1fr,) * columns
        },

        // 水平对齐
        align: horizon,

        inset: 8pt,
        stroke: none,

        // 将分割后的数据作为表格内容
        ..cells,

        // 添加三线表样式
        // 顶线
        table.hline(y: 0, stroke: 1.5pt),
        // 表头下线 (假设第一行是表头)
        table.hline(y: 1, stroke: 0.8pt),
        // 底线 (需要计算总行数)
        table.hline(y: calc.ceil(cells.len() / columns), stroke: 1.5pt),
      )
    ]
  }
}