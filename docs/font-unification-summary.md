# 字体统一设置总结

## 概述

根据引用标准，已将整个文档系统的字体统一设置为：
```
font: ("STIX Two Text", "Noto Serif CJK SC")
```

这确保了英文使用 STIX Two Text 字体，中文使用 Noto Serif CJK SC 字体，保持了整个文档的字体一致性。

## 修改的文件和内容

### 1. 表格系统 (`settings/table.typ`)

**修改内容：**
- ✅ 所有表格标题的字体设置
- ✅ 表格内容的字体设置
- ✅ 涵盖所有表格函数：
  - `three-line-table`
  - `load-csv-simple`
  - `load-csv-adaptive`
  - `adaptive-width-table`
  - `mixed-width-table`
  - `manual-table`
  - `manual-adaptive-table`

**示例修改：**
```typst
// 修改前
set text(size: 10.5pt)
#text(weight: "bold")[表 #chapter-num - #table-num: #caption]

// 修改后
set text(
  font: ("STIX Two Text", "Noto Serif CJK SC"),
  size: 10.5pt
)
#text(
  font: ("STIX Two Text", "Noto Serif CJK SC"),
  weight: "bold"
)[表 #chapter-num - #table-num: #caption]
```

### 2. 图片系统 (`settings/figure.typ`)

**修改内容：**
- ✅ 图片标题的字体设置
- ✅ 子图标记 (a), (b), (c) 的字体设置
- ✅ 涵盖所有图片函数：
  - `img`
  - `side-by-side-figures`
  - `three-figures`
  - `grid-figures`
  - `two-images`

**示例修改：**
```typst
// 修改前
#text(size: 10pt, weight: "bold")[
  图 #chapter-num - #figure-num：#caption
]

// 修改后
#text(
  font: ("STIX Two Text", "Noto Serif CJK SC"),
  size: 10pt, 
  weight: "bold"
)[
  图 #chapter-num - #figure-num：#caption
]
```

### 3. 页眉页脚系统 (`settings/main.typ`, `settings/template.typ`)

**修改内容：**
- ✅ 页眉中的"复旦大学博士学位论文"字体
- ✅ 页脚中的页码字体（通过全局设置继承）

**示例修改：**
```typst
// 修改前
header: [
  #set text(size: 10pt)
  #align(center)[复旦大学博士学位论文]
  #v(0.3em)
  #line(length: 100%, stroke: 0.5pt)
]

// 修改后
header: [
  #set text(
    font: ("STIX Two Text", "Noto Serif CJK SC"),
    size: 10pt
  )
  #align(center)[复旦大学博士学位论文]
  #v(0.3em)
  #line(length: 100%, stroke: 0.5pt)
]
```

### 4. 目录系统 (`settings/toc.typ`)

**修改内容：**
- ✅ 目录标题"目录"的字体
- ✅ 一级标题的字体
- ✅ 二级标题的字体
- ✅ 三级标题的字体

**示例修改：**
```typst
// 修改前
#let toc-title() = {
  align(center, text(20pt, weight: "bold")[目录])
  v(1em)
}

// 修改后
#let toc-title() = {
  align(center, text(
    font: ("STIX Two Text", "Noto Serif CJK SC"),
    20pt, 
    weight: "bold"
  )[目录])
  v(1em)
}
```

### 5. 封面系统 (`settings/cover.typ`)

**修改内容：**
- ✅ 中文封面所有文字的字体
- ✅ 英文封面所有文字的字体
- ✅ 指导小组成员页面的字体

**示例修改：**
```typst
// 修改前
#text(size: 22pt)[博士学位论文]

// 修改后
#text(
  font: ("STIX Two Text", "Noto Serif CJK SC"),
  size: 22pt
)[博士学位论文]
```

### 6. 模板系统 (`settings/template.typ`)

**修改内容：**
- ✅ 标准标题样式（摘要、参考文献等）
- ✅ 图表标题定义
- ✅ 参考文献样式
- ✅ 引用样式

**示例修改：**
```typst
// 修改前
#show heading.where(level: 1): it => {
  v(1.5em)
  align(center, text(22pt, weight: "bold")[#it.body])
  v(0.5em)
}

// 修改后
#show heading.where(level: 1): it => {
  v(1.5em)
  align(center, text(
    font: ("STIX Two Text", "Noto Serif CJK SC"),
    22pt, 
    weight: "bold"
  )[#it.body])
  v(0.5em)
}
```

### 7. 标题系统 (`settings/heading.typ`)

**状态：** ✅ 已经正确设置
- 所有级别的标题都已经使用了正确的字体设置

## 字体设置的层次结构

1. **全局字体设置** (`settings/template.typ`)
   - 正文内容的基础字体

2. **局部字体设置** (各个专门文件)
   - 表格标题和内容
   - 图片标题和标记
   - 页眉页脚
   - 目录
   - 封面
   - 各级标题

## 验证方法

编译文档后，检查以下内容是否都使用了统一字体：
- [ ] 表格标题和内容
- [ ] 图片标题和子图标记
- [ ] 页眉中的"复旦大学博士学位论文"
- [ ] 目录标题和各级条目
- [ ] 封面所有文字
- [ ] 章节标题
- [ ] 参考文献
- [ ] 引用标记

## 注意事项

1. **字体优先级**：局部设置会覆盖全局设置
2. **一致性**：所有文字元素现在都使用相同的字体族
3. **兼容性**：STIX Two Text 处理英文，Noto Serif CJK SC 处理中文
4. **维护性**：如需更改字体，需要在所有相关文件中同步修改

## 完成状态

✅ **已完成** - 所有文档元素的字体已统一设置为 `("STIX Two Text", "Noto Serif CJK SC")`
